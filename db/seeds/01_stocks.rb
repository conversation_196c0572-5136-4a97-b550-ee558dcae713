# frozen_string_literal: true

stocks = [
  { ticker: "AAPL",
    stock_exchange: "NASDAQ",
    company_name: "Apple Inc.",
    company_website: "https://www.apple.com",
    description: "Apple Inc. specializes in the conceptualization, production, and distribution of smartphones, personal computers, tablets, wearable technology. ",
    industry: "Computers/Consumer Electronics",
    management: "<PERSON> (CEO), <PERSON><PERSON> (CFO)",
    employees: 164_000,
    competitor_names: "Samsung, Google, Microsoft, Amazon, Meta",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 19_918 },
  { ticker: "KO",
    stock_exchange: " ",
    company_name: "Coca Cola",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 32_234 },
  {
    ticker: "DIS",
    stock_exchange: " ",
    company_name: "Disney",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 319_910
  },
  { ticker: "VZ",
    stock_exchange: " ",
    company_name: "Verizon",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 20_500 },
  { ticker: "LUV",
    stock_exchange: "NYSE",
    company_name: "Southwest",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 9_325 },
  { ticker: "UAA",
    stock_exchange: " ",
    company_name: "Under Armour",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 11_223 },
  { ticker: "GAP",
    stock_exchange: " ",
    company_name: "Gap",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 10_945 },
  {
    ticker: "F",
    stock_exchange: " ",
    company_name: "Ford",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 319_000
  },
  {
    ticker: "SONY",
    stock_exchange: " ",
    company_name: "Sony",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 99_231
  },
  {
    ticker: "SIRI",
    stock_exchange: " ",
    company_name: "Sirius XM",
    company_website: " ",
    description: " ",
    industry: " ",
    management: " ",
    employees: 164_000,
    competitor_names: " ",
    sales_growth: 2.02,
    industry_avg_sales_growth: 5.50,
    debt_to_equity: 1.73,
    industry_avg_debt_to_equity: 0.85,
    profit_margin: 23.97,
    industry_avg_profit_margin: 12.50,
    cash_flow: 24_000_000_000.00,
    debt: 95_000_000_000.00,
    price_cents: 32_213
  }
]

# finding an existing stock or create using only company_name for now
stocks.each do |stock_data|
  stock = Stock.find_or_create_by(company_name: stock_data[:company_name])
  stock.update!(stock_data)
end
