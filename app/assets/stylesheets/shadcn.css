@layer base {
  :root {
    --font-sans: "Inter var";
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --success: 132, 95.3%, 33.3%, 0.74;
    --success-foreground: 109 55% 28%;

    --info: 223 78% 42%;
    --info-foreground: 225 100% 50%;

    --attention: 45 90% 45%;
    --attention-foreground: 60 98.4% 48.8% 0.82;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --attention: 45, 90%, 45%, 0.8;
    --attention-foreground: 60 98.4% 48.8% 0.82;

    --success: 109 55% 28%;
    --success-foreground: 109 55% 28%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }

  .code-sample {
    @apply max-h-[650px] min-h-[350px] overflow-x-auto rounded-lg border bg-zinc-950 dark:bg-zinc-900;
  }

  .code-sample pre {
    @apply min-h-[350px];
  }

  .code-sample span {
    @apply bg-zinc-950;
  }
}
input[type="range"] {
  display: inline-block;
  vertical-align: middle;
  font-size: 1em;
  font-family: Arial, sans-serif;
}

/* input[type="range"]:focus,
input[type="number"]:focus {
  box-shadow: 0 0 3px 1px #4b81dd;
  outline: none;
} */

input[type="range"] {
  -webkit-appearance: none;
  margin-right: 15px;
  min-width: 200px;
  height: 7px;
  background: #f4f4f5;
  border-radius: 5px;
  background-image: linear-gradient(#000, #000);
  background-repeat: no-repeat;
}

/* Input Thumb */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #fff;
  cursor: ew-resize;
  box-shadow: 0 0 2px 0 #000;
  transition: background 0.3s ease-in-out;
}

input[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #fff;
  cursor: ew-resize;
  box-shadow: 0 0 2px 0 #000;
  transition: background 0.3s ease-in-out;
}

input[type="range"]::-ms-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #fff;
  cursor: ew-resize;
  box-shadow: 0 0 2px 0 #000;
  transition: background 0.3s ease-in-out;
}

/* input[type="range"]::-webkit-slider-thumb:hover {
  background: #a1a1aa;
}

input[type="range"]::-moz-range-thumb:hover {
  background: #a1a1aa;
}

input[type="range"]::-ms-thumb:hover {
  background: #a1a1aa;
} */

input[type="range"]::-moz-range-track {
  -webkit-appearance: none;
  box-shadow: none;
  border: none;
  background: transparent;
}

input[type="range"]::-ms-track {
  -webkit-appearance: none;
  box-shadow: none;
  border: none;
  background: transparent;
}

.st-accordion .st-accordion__icon:before {
  content: "▼";
  display: inline-block;
  margin-right: 5px;
  font-size: 80%;
  text-decoration: none;
  transform: rotate(-90deg);
}
.st-accordion .st-accordion__icon--opened:before {
  transform: rotate(0deg);
}

.st-accordion .st-accordion__content:not(.st-accordion__content--visible) {
  height: 0;
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
  transition: all 0.2s;
}
.st-accordion .st-accordion__content.st-accordion__content--visible {
  opacity: 1;
  visibility: visible;
  overflow: hidden;
  transition: all 0.2s;
}

input.error {
  @apply border-red-400;
}

label.error {
  @apply text-destructive;
}
