@import 'shadcn.css';

/* SIF custom CSS */
@import 'custom/buttons.css';
@import 'custom/forms.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .navbar {
    @apply bg-white shadow-sm;
  }
  .navbar-container {
    @apply mx-auto max-w-7xl px-2 sm:px-6 lg:px-8;
  }
  .navbar-layout {
    @apply relative flex h-16 justify-between;
  }
  .navbar-mobile-button-container {
    @apply absolute inset-y-0 left-0 flex items-center sm:hidden;
  }
  .navbar-mobile-button {
    @apply relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:ring-2 focus:ring-indigo-500 focus:outline-none focus:ring-inset;
  }
  .navbar-svg-icon {
    @apply size-6;
  }
  .navbar-left-section {
    @apply flex flex-1 items-center justify-start sm:items-stretch sm:justify-start;
  }
  .navbar-logo-container {
    @apply flex shrink-0 items-center;
  }
  .navbar-logo {
    @apply h-8 w-auto;
.nav-user-link {
    @apply text-gray-900 font-medium;
  }
  }
  .navbar-main-links-container {
    @apply hidden sm:ml-6 sm:flex sm:space-x-8;
  }
  .navbar-main-link {
    @apply inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium;
  }
  .navbar-main-link-active {
    @apply border-[#00B8B0] text-gray-900;
  }
  .navbar-main-link-default {
    @apply border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700;
  }
  .navbar-right-section {
    @apply absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0;
  }
  .navbar-user-links-container {
    @apply flex space-x-4;
  }
  .navbar-notification-button {
    @apply relative rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none;
  }
}
