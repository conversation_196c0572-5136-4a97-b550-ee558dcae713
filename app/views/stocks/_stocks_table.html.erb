<table class="table-auto">
  <thead>
    <tr>
      <th class="px-4 py-2">Company (Exchange)</th>
      <th class="px-4 py-2">Last Price</th>
      <% if current_user.student? %>
        <th class="px-4 py-2">Your Holdings</th>
      <% end %>
      <th class="px-4 py-2"></th>
    </tr>
  </thead>
  <tbody>
  <% stocks.each do |stock| %>
    <tr>
      <td class="px-4 py-2">
        <%= link_to stock.ticker, stock_path(stock) %><br>
        <span class="text-gray-500">(<%= stock.company_name %>)</span>
      </td>
      <td class="px-4 py-2">
        <%= number_to_currency(stock.current_price) %>
      </td>
      <% if current_user.student? %>
      <td class="px-4 py-2">
        <%= current_user.portfolio.shares_owned(stock.id) %>
      </td>
        <td class="px-4 py-2">
            <%= link_to "Buy", new_order_path(stock_id: stock.id, transaction_type: :buy), class: "rounded-md bg-white px-4 py-2 text-sm font-semibold text-gray-900 shadow-xs ring-1 ring-gray-300 ring-inset hover:bg-gray-50" %>
        </td>
        <td class="px-4 py-2">
            <%= link_to "Sell", new_order_path(stock_id: stock.id, transaction_type: :sell), class: "rounded-md bg-[#00698c] px-4 py-2 text-sm font-semibold text-white shadow-xs hover:bg-[#004f6b] focus-visible:outline-2 focus-visible:outline-offset-2" %>
        </td>
      <% end %>
    </tr>
  <% end %>
  </tbody>
</table>
