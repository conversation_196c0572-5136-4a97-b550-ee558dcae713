<%= form_with(model: classroom, class: "contents") do |form| %>
  <% if classroom.errors.any? %>
    <div id="error_explanation" class="bg-red-50 text-red-500 px-3 py-2 font-medium rounded-lg mt-3">
      <h2><%= pluralize(classroom.errors.count, "error") %> prohibited this classroom from being saved:</h2>

      <ul>
        <% classroom.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="my-5">
    <%= form.label :name, class: 'tw-label-primary' %>
    <div class="mt-2">
      <%= form.text_field :name, class: "tw-input-primary" %>
    </div>
  </div>

  <div class="my-5">
    <%= form.label :school_id, "School Name", class: 'tw-label-primary' %>
    <div class="mt-2">
      <%= form.select :school_id,
          options_from_collection_for_select(@schools, :id, :name, classroom&.school&.id),
          { prompt: "Select a school" },
          { class: "tw-input-primary" } %>
    </div>
  </div>

  <div class="my-5">
    <%= form.label :year_id, "Year", class: 'tw-label-primary' %>
    <div class="mt-2">
      <%= form.select :year_id,
          options_from_collection_for_select(@years, :id, :name, classroom&.year&.id),
          { prompt: "Select a year" },
          { class: "tw-input-primary" } %>
    </div>
  </div>

  <div class="my-5">
    <%= form.label :grade, class: 'tw-label-primary' %>
    <div class="mt-2">
      <%= form.text_field :grade, class: "tw-input-primary" %>
    </div>
  </div>

  <div class="inline">
    <%= form.submit class: "tw-btn-primary" %>
  </div>
<% end %>
