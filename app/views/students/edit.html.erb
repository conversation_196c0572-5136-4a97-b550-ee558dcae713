<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
  <h1 class="text-2xl font-bold text-gray-900 mb-6">Edit Student</h1>

  <%= form_with model: [@classroom, @student], local: true, class: "space-y-6" do |f| %>
    <% if @student.errors.any? %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">Error:</strong>
        <ul>
          <% @student.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-6 sm:col-span-4">
            <%= f.label :username, class: "block text-sm font-medium text-gray-700" %>
            <%= f.text_field :username, class: "mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", required: true %>
          </div>
        </div>
      </div>
      <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
        <%= f.submit "Update Student", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        <%= link_to "Cancel", classroom_path(@classroom), class: "ml-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      </div>
    </div>
  <% end %>
</div>