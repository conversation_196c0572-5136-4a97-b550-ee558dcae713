<!-- TODO: make price display with two decimal places -->
<div class="flex flex-col h-full w-full">
  <header class="flex items-center justify-between px-6 py-4 border-b">
    <h1 class="text-2xl font-bold">
        <%= @portfolio.user.username %>'s Portfolio
    </h1>
  </header>
  <main class="flex-1 grid grid-cols-1 lg:grid-cols-[1fr_2fr] gap-6 p-6">
    <div class="flex flex-col gap-6">
      <div class="bg-white dark:bg-gray-950 rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">Stocks</h2>
        <div class="space-y-4">
          <% @portfolio.portfolio_stocks.each do |stock| %>
            <!-- stock cards in list with dividers -->
            <div class="grid grid-cols-[1fr_auto_auto] items-center gap-4<% unless stock == @portfolio.portfolio_stocks.last %> pb-4 border-b border-gray-200<% end %>">
              <%= render partial: 'stock_card', locals: { portfolio_stock: stock } %>
            </div>
          <% end %>
        </div>
      </div>
      <div class="bg-white dark:bg-gray-950 rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">Cash Balance</h2>
        <div class="text-4xl font-bold">$<%=@portfolio.cash_balance%></div>
      </div>
      <div class="bg-white dark:bg-gray-950 rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">Current Position</h2>
        <div class="text-4xl font-bold">$<%=@portfolio.current_position%></div>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          <!-- TODO: calculate this, will probably need to add a log of cash_balances (this will also help with the graph) -->
          +20.1% from last month
        </p>
      </div>
    </div>
    <div class="bg-white dark:bg-gray-950 rounded-lg shadow p-6">
      <h2 class="text-lg font-semibold mb-4">Portfolio Trends</h2>
      <div class="aspect-[9/4]">
        <div style="width:100%;height:100%"></div>
        <!-- this is where we put the graph partial -->
      </div>
    </div>
  </main>
</div>
