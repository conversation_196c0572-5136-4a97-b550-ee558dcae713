<!-- TODO: make price display with two decimal places -->
<div class="grid grid-cols-[1fr_auto_auto] items-center gap-4">
    <% stock = Stock.find_by_id(portfolio_stock.stock_id) %>
    <div>
        <h3 class="font-medium">
            <%= stock.company_name %> (<%= stock.ticker %>)
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400">
            <%= portfolio_stock.shares %> shares
        </p>
    </div>
    <div class="text-right">
        <p class="font-medium">
            <!-- Current Price -->
            <!-- will be calculated from `stock.price` -->
            $120.00
        </p>
        <p class="text-sm text-gray-500 dark:text-gray-400">
            <!-- Purchased at price -->
            $ <%= portfolio_stock.purchase_price %>
        </p>
    </div>
    <div class="flex gap-2">
        <button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3">
            <!-- TODO: link to `Buy` transaction -->
            Buy
        </button>

        <button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3">
            <!-- TODO: link to `Sell` transaction -->
            Sell
        </button>
    </div>
</div>
