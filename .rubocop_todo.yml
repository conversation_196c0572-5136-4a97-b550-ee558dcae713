# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2025-07-03 14:45:34 UTC using RuboCop version 1.77.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
# Configuration parameters: AllowComments, AllowEmptyLambdas.
Lint/EmptyBlock:
  Exclude:
    - 'test/factories/schools.rb'

# Offense count: 1
# Configuration parameters: AllowedMethods, AllowedPatterns, CountRepeatedAttributes, Max.
Metrics/AbcSize:
  Exclude:
    - 'test/models/sti_test.rb'

# Offense count: 2
# Configuration parameters: CountComments, CountAsOne.
Metrics/ClassLength:
  Max: 120

# Offense count: 2
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/CyclomaticComplexity:
  Max: 10

# Offense count: 2
# Configuration parameters: Count<PERSON>om<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, AllowedMethods, AllowedPatterns.
Metrics/MethodLength:
  Exclude:
    - 'app/helpers/components/input_helper.rb'
    - 'test/models/sti_test.rb'

# Offense count: 2
# Configuration parameters: CountKeywordArgs, MaxOptionalParameters.
Metrics/ParameterLists:
  Max: 7

# Offense count: 1
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/PerceivedComplexity:
  Max: 10

# Offense count: 3
# Configuration parameters: Include.
# Include: **/app/models/**/*.rb
Rails/HasManyOrHasOneDependent:
  Exclude:
    - 'app/models/school.rb'
    - 'app/models/stock.rb'
