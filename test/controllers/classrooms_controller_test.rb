# frozen_string_literal: true

require "test_helper"

class ClassroomsControllerTest < ActionDispatch::IntegrationTest
  def setup
    @admin = create(:admin)
    @classroom = create(:classroom)
    @teacher = create(:teacher, classroom: @classroom)
    @student = create(:student, classroom: @classroom)
  end

  test "index" do
    sign_in(@admin)
    get classrooms_path
    assert_response :success
  end

  test "new" do
    sign_in(@admin)
    get new_classroom_path
    assert_response :success
  end

  test "create" do
    sign_in(@admin)
    school = create(:school)
    year = create(:year)
    params = { classroom: { name: "Test Class", grade: "5th", school_id: school.id, year_id: year.id } }
    assert_difference("Classroom.count") do
      post(classrooms_path, params:)
    end
    assert_redirected_to classroom_path(Classroom.last)
    assert_equal t("classrooms.create.notice"), flash[:notice]
  end

  test "show" do
    sign_in(@admin)
    get classroom_path(@classroom)
    assert_response :success
  end

  test "edit" do
    sign_in(@admin)
    get edit_classroom_path(@classroom)
    assert_response :success
  end

  test "update" do
    school = create(:school)
    year = create(:year)
    params = { classroom: { name: "Abc123", grade: "6th", school_id: school.id, year_id: year.id } }
    sign_in(@admin)
    assert_changes "@classroom.reload.updated_at" do
      patch(classroom_path(@classroom), params:)
    end
    assert_redirected_to classroom_path(@classroom)
    assert_equal t("classrooms.update.notice"), flash[:notice]
  end

  test "destroy" do
    sign_in(@admin)
    assert_difference("Classroom.count", -1) do
      delete classroom_path(@classroom)
    end
    assert_redirected_to classrooms_path
    assert_equal t("classrooms.destroy.notice"), flash[:notice]
  end

  test "admins can see all classrooms in index" do
    classroom1 = create(:classroom, name: "Class 1")
    classroom2 = create(:classroom, name: "Class 2")
    sign_in(@admin)
    get classrooms_path
    assert_response :success
    assert_includes response.body, classroom1.name
    assert_includes response.body, classroom2.name
  end

  test "show includes student management for teachers" do
    create(:portfolio, user: @student)
    sign_in @teacher
    get classroom_path(@classroom)
    assert_response :success
    assert_includes response.body, @student.username
  end

  test "teachers can only see their own classroom in index" do
    classroom1 = create(:classroom, name: "Teacher 1 Class")
    classroom2 = create(:classroom, name: "Teacher 2 Class")
    teacher = create(:teacher, classroom: classroom1)
    sign_in teacher
    get classrooms_path
    assert_response :success
    assert_includes response.body, classroom1.name
    assert_not_includes response.body, classroom2.name
  end

  test "students cannot create classrooms" do
    sign_in @student
    get new_classroom_path
    assert_redirected_to root_path
  end

  test "students cannot edit classrooms" do
    sign_in @student
    get edit_classroom_path(@classroom)
    assert_redirected_to root_path
  end

  test "students cannot delete classrooms" do
    sign_in @student
    assert_no_difference("Classroom.count") do
      delete classroom_path(@classroom)
    end
    assert_redirected_to root_path
  end

  test "teachers cannot create classrooms" do
    sign_in @teacher
    get new_classroom_path
    assert_redirected_to root_path
  end

  test "teachers cannot edit classrooms" do
    sign_in @teacher
    get edit_classroom_path(@classroom)
    assert_redirected_to root_path
  end

  test "teachers cannot  delete classrooms" do
    sign_in @teacher
    assert_no_difference("Classroom.count") do
      delete classroom_path(@classroom)
    end
    assert_redirected_to root_path
  end

  # Tests for dropdown functionality
  test "new action renders form with school and year dropdowns" do
    school1 = create(:school, name: "Elementary School")
    school2 = create(:school, name: "High School")
    year1 = create(:year, name: "2023-2024")
    year2 = create(:year, name: "2024-2025")

    sign_in(@admin)
    get new_classroom_path

    assert_response :success
    assert_select "select[name='classroom[school_id]']" do
      assert_select "option[value='#{school1.id}']", text: school1.name
      assert_select "option[value='#{school2.id}']", text: school2.name
    end
    assert_select "select[name='classroom[year_id]']" do
      assert_select "option[value='#{year1.id}']", text: year1.name
      assert_select "option[value='#{year2.id}']", text: year2.name
    end
  end

  test "edit action renders form with school and year dropdowns" do
    school1 = create(:school, name: "Elementary School")
    school2 = create(:school, name: "High School")
    year1 = create(:year, name: "2023-2024")
    year2 = create(:year, name: "2024-2025")

    sign_in(@admin)
    get edit_classroom_path(@classroom)

    assert_response :success
    assert_select "select[name='classroom[school_id]']" do
      assert_select "option[value='#{school1.id}']", text: school1.name
      assert_select "option[value='#{school2.id}']", text: school2.name
    end
    assert_select "select[name='classroom[year_id]']" do
      assert_select "option[value='#{year1.id}']", text: year1.name
      assert_select "option[value='#{year2.id}']", text: year2.name
    end
  end

  test "create with valid school_id and year_id creates classroom with correct school_year" do
    school = create(:school, name: "Test School")
    year = create(:year, name: "2024-2025")

    sign_in(@admin)
    params = { classroom: { name: "Test Class", grade: "5th", school_id: school.id, year_id: year.id } }

    assert_difference("Classroom.count") do
      post(classrooms_path, params:)
    end

    classroom = Classroom.last
    assert_equal school, classroom.school
    assert_equal year, classroom.year
  end

  test "update with valid school_id and year_id updates classroom school_year" do
    new_school = create(:school, name: "New School")
    new_year = create(:year, name: "2025-2026")

    sign_in(@admin)
    params = { classroom: { name: "Updated Class", school_id: new_school.id, year_id: new_year.id } }

    patch(classroom_path(@classroom), params:)

    @classroom.reload
    assert_equal new_school, @classroom.school
    assert_equal new_year, @classroom.year
  end

  test "edit form shows current school and year selected" do
    school = create(:school, name: "Current School")
    year = create(:year, name: "Current Year")
    school_year = create(:school_year, school: school, year: year)
    classroom = create(:classroom, school_year: school_year)

    sign_in(@admin)
    get edit_classroom_path(classroom)

    assert_response :success
    assert_select "select[name='classroom[school_id]']" do
      assert_select "option[value='#{school.id}'][selected='selected']", text: school.name
    end
    assert_select "select[name='classroom[year_id]']" do
      assert_select "option[value='#{year.id}'][selected='selected']", text: year.name
    end
  end
end
