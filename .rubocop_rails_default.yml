# Common configuration.

inherit_mode:
  merge:
    - Exclude

AllCops:
  Exclude:
    - app/assets/**/*
    - bin/*
    # Exclude db/schema.rb and db/[CONFIGURATION_NAMESPACE]_schema.rb by default.
    # See: https://guides.rubyonrails.org/active_record_multiple_databases.html#setting-up-your-application
    - db/*schema.rb
    - log/**/*
    - public/**/*
    - storage/**/*
  # Enable checking Active Support extensions.
  # See: https://docs.rubocop.org/rubocop/configuration.html#enable-checking-active-support-extensions
  ActiveSupportExtensionsEnabled: true
  # What version of Rails is the inspected code using? If a value is specified
  # for `TargetRailsVersion` then it is used. Acceptable values are specified
  # as a float (e.g., 7.2); the patch version of Rails should not be included.
  # If `TargetRailsVersion` is not set, RuboCop will parse the Gemfile.lock or
  # gems.locked file to find the version of Rails that has been bound to the
  # application. If neither of those files exist, RuboCop will use Rails 5.0
  # as the default.
  TargetRailsVersion: ~
  # By specifying `MigratedSchemaVersion` option, migration files that have been migrated can be ignored.
  # When `MigratedSchemaVersion: '20241231000000'` is set. Migration files lower than or equal to '20250101000000' will be ignored.
  # For example, this is the timestamp in db/migrate/20250101000000_create_articles.rb.
  MigratedSchemaVersion: '19700101000000' # NOTE: Used as a sentinel value for the UNIX epoch time.

Lint/NumberConversion:
  # Add Rails' duration methods to the ignore list for `Lint/NumberConversion`
  # so that calling `to_i` on one of these does not register an offense.
  # See: https://github.com/rubocop/rubocop/issues/8950
  AllowedMethods:
    - ago
    - from_now
    - second
    - seconds
    - minute
    - minutes
    - hour
    - hours
    - day
    - days
    - week
    - weeks
    - fortnight
    - fortnights
    - in_milliseconds
  AllowedPatterns: []

Lint/RedundantSafeNavigation:
  # Add `presence` and `present?` methods to the default of the RuboCop core.
  # https://github.com/rubocop/rubocop/blob/v1.51.0/config/default.yml#L2148-L2159
  AllowedMethods:
    - instance_of?
    - kind_of?
    - is_a?
    - eql?
    - respond_to?
    - equal?
    - presence
    - present?

Lint/SafeNavigationChain:
  # Add `presence_in` method to the default of the RuboCop core:
  # https://github.com/rubocop/rubocop/blob/v1.56.0/config/default.yml#L2265-L2271
  AllowedMethods:
    - present?
    - blank?
    - presence
    - presence_in
    - try
    - try!
    - in?

Lint/UselessAccessModifier:
  # Add methods from `ActiveSupport::Concern` and `Module::Concerning`:
  # https://api.rubyonrails.org/classes/ActiveSupport/Concern.html
  # https://api.rubyonrails.org/classes/Module/Concerning
  inherit_mode:
    merge:
      - ContextCreatingMethods
  ContextCreatingMethods:
    - class_methods
    - included
    - prepended
    - concern
    - concerning

Rails:
  Enabled: true
  DocumentationBaseURL: https://docs.rubocop.org/rubocop-rails

Rails/ActionControllerFlashBeforeRender:
  Description: 'Use `flash.now` instead of `flash` before `render`.'
  Enabled: 'pending'
  SafeAutoCorrect: false
  VersionAdded: '2.16'

Rails/ActionControllerTestCase:
  Description: 'Use `ActionDispatch::IntegrationTest` instead of `ActionController::TestCase`.'
  StyleGuide: 'https://rails.rubystyle.guide/#integration-testing'
  Reference: 'https://api.rubyonrails.org/classes/ActionController/TestCase.html'
  Enabled: 'pending'
  SafeAutoCorrect: false
  VersionAdded: '2.14'
  Include:
    - '**/test/**/*.rb'

Rails/ActionFilter:
  Description: 'Enforces consistent use of action filter methods.'
  Enabled: false
  VersionAdded: '0.19'
  VersionChanged: '2.22'
  EnforcedStyle: action
  SupportedStyles:
    - action
    - filter
  Include:
    - app/controllers/**/*.rb
    - app/mailers/**/*.rb

Rails/ActionOrder:
  Description: 'Enforce consistent ordering of controller actions.'
  Enabled: pending
  VersionAdded: '2.17'
  ExpectedOrder:
    - index
    - show
    - new
    - edit
    - create
    - update
    - destroy
  Include:
    - app/controllers/**/*.rb

Rails/ActiveRecordAliases:
  Description: >-
                  Avoid Active Record aliases:
                  Use `update` instead of `update_attributes`.
                  Use `update!` instead of `update_attributes!`.
  Enabled: true
  VersionAdded: '0.53'
  SafeAutoCorrect: false

Rails/ActiveRecordCallbacksOrder:
  Description: 'Order callback declarations in the order in which they will be executed.'
  StyleGuide: 'https://rails.rubystyle.guide/#callbacks-order'
  Enabled: 'pending'
  VersionAdded: '2.7'
  Include:
    - app/models/**/*.rb

Rails/ActiveRecordOverride:
  Description: >-
                  Check for overriding Active Record methods instead of using
                  callbacks.
  Enabled: true
  Severity: warning
  VersionAdded: '0.67'
  VersionChanged: '2.18'
  Include:
    - app/models/**/*.rb

Rails/ActiveSupportAliases:
  Description: >-
                  Avoid ActiveSupport aliases of standard ruby methods:
                  `String#starts_with?`, `String#ends_with?`,
                  `Array#append`, `Array#prepend`.
  Enabled: true
  VersionAdded: '0.48'

Rails/ActiveSupportOnLoad:
  Description: 'Use `ActiveSupport.on_load(...)` to patch Rails framework classes.'
  Enabled: 'pending'
  Reference:
    - 'https://api.rubyonrails.org/classes/ActiveSupport/LazyLoadHooks.html'
    - 'https://guides.rubyonrails.org/engines.html#available-load-hooks'
  SafeAutoCorrect: false
  VersionAdded: '2.16'
  VersionChanged: '2.24'

Rails/AddColumnIndex:
  Description: >-
    Rails migrations don't make use of a given `index` key, but also
    doesn't given an error when it's used, so it makes it seem like an
    index might be used.
  Enabled: pending
  VersionAdded: '2.11'
  VersionChanged: '2.20'
  Include:
    - db/**/*.rb

Rails/AfterCommitOverride:
  Description: >-
                  Enforces that there is only one call to `after_commit`
                  (and its aliases - `after_create_commit`, `after_update_commit`,
                  and `after_destroy_commit`) with the same callback name per model.
  Enabled: 'pending'
  VersionAdded: '2.8'

Rails/ApplicationController:
  Description: 'Check that controllers subclass ApplicationController.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '2.4'
  VersionChanged: '2.5'

Rails/ApplicationJob:
  Description: 'Check that jobs subclass ApplicationJob.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '0.49'
  VersionChanged: '2.5'

Rails/ApplicationMailer:
  Description: 'Check that mailers subclass ApplicationMailer.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '2.4'
  VersionChanged: '2.5'

Rails/ApplicationRecord:
  Description: 'Check that models subclass ApplicationRecord.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '0.49'
  VersionChanged: '2.26'
  Exclude:
    - db/**/*.rb

Rails/ArelStar:
  Description: 'Enforces `Arel.star` instead of `"*"` for expanded columns.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '2.9'

Rails/AssertNot:
  Description: 'Use `assert_not` instead of `assert !`.'
  Enabled: true
  VersionAdded: '0.56'
  Include:
    - '**/test/**/*'

Rails/AttributeDefaultBlockValue:
  Description: 'Pass method call in block for attribute option `default`.'
  Enabled: pending
  VersionAdded: '2.9'
  Include:
    - 'app/models/**/*'

Rails/BelongsTo:
  Description: >-
                  Use `optional: true` instead of `required: false` for
                  `belongs_to` relations.
  Reference:
    - https://guides.rubyonrails.org/5_0_release_notes.html
    - https://github.com/rails/rails/pull/18937
  Enabled: true
  VersionAdded: '0.62'

Rails/Blank:
  Description: 'Enforces use of `blank?`.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '0.48'
  VersionChanged: '2.10'
  # Convert usages of `nil? || empty?` to `blank?`
  NilOrEmpty: true
  # Convert usages of `!present?` to `blank?`
  NotPresent: true
  # Convert usages of `unless present?` to `if blank?`
  UnlessPresent: true

Rails/BulkChangeTable:
  Description: 'Check whether alter queries are combinable.'
  Reference:
    - https://api.rubyonrails.org/classes/ActiveRecord/ConnectionAdapters/SchemaStatements.html#method-i-change_table
    - https://api.rubyonrails.org/classes/ActiveRecord/ConnectionAdapters/Table.html
  Enabled: true
  VersionAdded: '0.57'
  VersionChanged: '2.20'
  Database: null
  SupportedDatabases:
    - mysql
    - postgresql
  Include:
    - db/**/*.rb

Rails/CompactBlank:
  Description: 'Checks if collection can be blank-compacted with `compact_blank`.'
  Enabled: pending
  Safe: false
  VersionAdded: '2.13'

Rails/ContentTag:
  Description: 'Use `tag.something` instead of `tag(:something)`.'
  Reference:
    - 'https://github.com/rubocop/rubocop-rails/issues/260'
    - 'https://github.com/rails/rails/issues/25195'
    - 'https://api.rubyonrails.org/classes/ActionView/Helpers/TagHelper.html#method-i-content_tag'
  Enabled: true
  VersionAdded: '2.6'
  VersionChanged: '2.12'
  # This `Exclude` config prevents false positives for `tag` calls to `has_one: tag` and Puma configuration:
  # https://puma.io/puma/Puma/DSL.html#tag-instance_method
  # No helpers are used in normal models and configs.
  Exclude:
    - app/models/**/*.rb
    - config/**/*.rb

Rails/CreateTableWithTimestamps:
  Description: >-
                  Checks the migration for which timestamps are not included
                  when creating a new table.
  Enabled: true
  VersionAdded: '0.52'
  VersionChanged: '2.20'
  Include:
    - db/**/*.rb
  Exclude:
    # Respect the `active_storage_variant_records` table of `*_create_active_storage_tables.active_storage.rb`
    # and `*_create_active_storage_variant_records.active_storage.rb`
    # auto-generated by `bin/rails active_storage:install` even if `created_at` is not specified.
    - db/**/*_create_active_storage_tables.active_storage.rb
    - db/**/*_create_active_storage_variant_records.active_storage.rb

Rails/DangerousColumnNames:
  Description: >-
                  Avoid dangerous column names.
  Enabled: pending
  Severity: warning
  VersionAdded: '2.21'
  Include:
    - 'db/**/*.rb'

Rails/Date:
  Description: >-
                  Checks the correct usage of date aware methods,
                  such as Date.today, Date.current etc.
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '0.30'
  VersionChanged: '2.11'
  # The value `strict` disallows usage of `Date.today`, `Date.current`,
  # `Date#to_time` etc.
  # The value `flexible` allows usage of `Date.current`, `Date.yesterday`, etc
  # (but not `Date.today`) which are overridden by ActiveSupport to handle current
  # time zone.
  EnforcedStyle: flexible
  SupportedStyles:
    - strict
    - flexible
  AllowToTime: true

Rails/DefaultScope:
  Description: 'Avoid use of `default_scope`.'
  Enabled: false
  VersionAdded: '2.7'

Rails/Delegate:
  Description: 'Prefer delegate method for delegations.'
  Enabled: true
  VersionAdded: '0.21'
  VersionChanged: '2.30'
  # When set to true, using the target object as a prefix of the
  # method name without using the `delegate` method will be a
  # violation. When set to false, this case is legal.
  EnforceForPrefixed: true
  Exclude:
    - app/controllers/**/*.rb

Rails/DelegateAllowBlank:
  Description: 'Do not use allow_blank as an option to delegate.'
  Enabled: true
  VersionAdded: '0.44'

Rails/DeprecatedActiveModelErrorsMethods:
  Description: 'Avoid manipulating ActiveModel errors hash directly.'
  Enabled: pending
  Severity: warning
  Safe: false
  VersionAdded: '2.14'
  VersionChanged: '2.18'

Rails/DotSeparatedKeys:
  Description: 'Enforces the use of dot-separated keys instead of `:scope` options in `I18n` translation methods.'
  StyleGuide: 'https://rails.rubystyle.guide/#dot-separated-keys'
  Enabled: pending
  VersionAdded: '2.15'

Rails/DuplicateAssociation:
  Description: "Don't repeat associations in a model."
  Enabled: pending
  Severity: warning
  VersionAdded: '2.14'
  VersionChanged: '2.18'

Rails/DuplicateScope:
  Description: 'Multiple scopes share this same where clause.'
  Enabled: pending
  Severity: warning
  VersionAdded: '2.14'
  VersionChanged: '2.18'

Rails/DurationArithmetic:
  Description: 'Do not use duration as arithmetic operand with `Time.current`.'
  StyleGuide: 'https://rails.rubystyle.guide#duration-arithmetic'
  Enabled: pending
  VersionAdded: '2.13'

Rails/DynamicFindBy:
  Description: 'Use `find_by` instead of dynamic `find_by_*`.'
  StyleGuide: 'https://rails.rubystyle.guide#find_by'
  Enabled: true
  Safe: false
  VersionAdded: '0.44'
  VersionChanged: '2.10'
  # The `Whitelist` has been deprecated, Please use `AllowedMethods` instead.
  Whitelist:
    - find_by_sql
    - find_by_token_for
  AllowedMethods:
    - find_by_sql
    - find_by_token_for
  AllowedReceivers:
    - Gem::Specification
    - page # Prevents a warning for `page.find_by_id`. See: https://github.com/rubocop/rubocop-rails/issues/778

Rails/EagerEvaluationLogMessage:
  Description: 'Checks that blocks are used for interpolated strings passed to `Rails.logger.debug`.'
  Reference: 'https://guides.rubyonrails.org/debugging_rails_applications.html#impact-of-logs-on-performance'
  Enabled: pending
  VersionAdded: '2.11'

Rails/EnumHash:
  Description: 'Prefer hash syntax over array syntax when defining enums.'
  StyleGuide: 'https://rails.rubystyle.guide#enums'
  Enabled: true
  VersionAdded: '2.3'
  Include:
    - app/models/**/*.rb

Rails/EnumSyntax:
  Description: 'Use positional arguments over keyword arguments when defining enums.'
  Enabled: pending
  Severity: warning
  VersionAdded: '2.26'
  Include:
    - app/models/**/*.rb

Rails/EnumUniqueness:
  Description: 'Avoid duplicate integers in hash-syntax `enum` declaration.'
  Enabled: true
  VersionAdded: '0.46'
  Include:
    - app/models/**/*.rb

Rails/EnvLocal:
  Description: 'Use `Rails.env.local?` instead of `Rails.env.development? || Rails.env.test?`.'
  Enabled: pending
  VersionAdded: '2.22'

Rails/EnvironmentComparison:
  Description: "Favor `Rails.env.production?` over `Rails.env == 'production'`."
  Enabled: true
  VersionAdded: '0.52'

Rails/EnvironmentVariableAccess:
  Description: 'Do not access `ENV` directly after initialization.'
  # TODO: Set to `pending` status in RuboCop Rails 2 series when migration doc will be written.
  Enabled: false
  VersionAdded: '2.10'
  VersionChanged: '2.24'
  Include:
    - app/**/*.rb
    - config/initializers/**/*.rb
    - lib/**/*.rb
  Exclude:
    - lib/**/*.rake
  AllowReads: false
  AllowWrites: false

Rails/Exit:
  Description: >-
                  Favor `fail`, `break`, `return`, etc. over `exit` in
                  application or library code outside of Rake files to avoid
                  exits during unit testing or running in production.
  Enabled: true
  VersionAdded: '0.41'
  Include:
    - app/**/*.rb
    - config/**/*.rb
    - lib/**/*.rb
  Exclude:
    - lib/**/*.rake

Rails/ExpandedDateRange:
  Description: 'Checks for expanded date range.'
  StyleGuide: 'https://rails.rubystyle.guide/#date-time-range'
  Enabled: pending
  VersionAdded: '2.11'

Rails/FilePath:
  Description: 'Use `Rails.root.join` for file path joining.'
  Enabled: true
  VersionAdded: '0.47'
  VersionChanged: '2.4'
  EnforcedStyle: slashes
  SupportedStyles:
    - slashes
    - arguments

Rails/FindBy:
  Description: 'Prefer find_by over where.first.'
  StyleGuide: 'https://rails.rubystyle.guide#find_by'
  Enabled: true
  VersionAdded: '0.30'
  VersionChanged: '2.21'
  IgnoreWhereFirst: true

Rails/FindById:
  Description: >-
                Favor the use of `find` over `where.take!`, `find_by!`, and `find_by_id!` when you
                need to retrieve a single record by primary key when you expect it to be found.
  StyleGuide: 'https://rails.rubystyle.guide/#find'
  Enabled: 'pending'
  VersionAdded: '2.7'

Rails/FindEach:
  Description: 'Prefer all.find_each over all.each.'
  StyleGuide: 'https://rails.rubystyle.guide#find-each'
  Enabled: true
  Safe: false
  VersionAdded: '0.30'
  VersionChanged: '2.21'
  AllowedMethods:
    # Methods that don't work well with `find_each`.
    - order
    - limit
    - select
    - lock
  AllowedPatterns: []

Rails/FreezeTime:
  Description: 'Prefer `freeze_time` over `travel_to` with an argument of the current time.'
  StyleGuide: 'https://rails.rubystyle.guide/#freeze-time'
  Enabled: pending
  VersionAdded: '2.16'
  SafeAutoCorrect: false

Rails/HasAndBelongsToMany:
  Description: 'Prefer has_many :through to has_and_belongs_to_many.'
  StyleGuide: 'https://rails.rubystyle.guide#has-many-through'
  Enabled: true
  VersionAdded: '0.12'
  Include:
    - app/models/**/*.rb

Rails/HasManyOrHasOneDependent:
  Description: 'Define the dependent option to the has_many and has_one associations.'
  StyleGuide: 'https://rails.rubystyle.guide#has_many-has_one-dependent-option'
  Enabled: true
  VersionAdded: '0.50'
  Include:
    - app/models/**/*.rb

Rails/HelperInstanceVariable:
  Description: 'Do not use instance variables in helpers.'
  Enabled: true
  VersionAdded: '2.0'
  Include:
    - app/helpers/**/*.rb

Rails/HttpPositionalArguments:
  Description: 'Use keyword arguments instead of positional arguments in http method calls.'
  Enabled: true
  VersionAdded: '0.44'
  Include:
    - 'spec/**/*'
    - 'test/**/*'

Rails/HttpStatus:
  Description: 'Enforces use of symbolic or numeric value to define HTTP status.'
  Enabled: true
  VersionAdded: '0.54'
  VersionChanged: '2.11'
  EnforcedStyle: symbolic
  SupportedStyles:
    - numeric
    - symbolic

Rails/I18nLazyLookup:
  Description: 'Checks for places where I18n "lazy" lookup can be used.'
  StyleGuide: 'https://rails.rubystyle.guide/#lazy-lookup'
  Reference: 'https://guides.rubyonrails.org/i18n.html#lazy-lookup'
  Enabled: pending
  VersionAdded: '2.14'
  EnforcedStyle: lazy
  SupportedStyles:
    - lazy
    - explicit
  Include:
    - 'app/controllers/**/*.rb'

Rails/I18nLocaleAssignment:
  Description: 'Prefer the usage of `I18n.with_locale` instead of manually updating `I18n.locale` value.'
  Enabled: 'pending'
  VersionAdded: '2.11'
  Include:
    - spec/**/*.rb
    - test/**/*.rb

Rails/I18nLocaleTexts:
  Description: 'Enforces use of I18n and locale files instead of locale specific strings.'
  StyleGuide: 'https://rails.rubystyle.guide/#locale-texts'
  Enabled: pending
  VersionAdded: '2.14'

Rails/IgnoredColumnsAssignment:
  Description: 'Looks for assignments of `ignored_columns` that override previous assignments.'
  StyleGuide: 'https://rails.rubystyle.guide/#append-ignored-columns'
  Enabled: pending
  SafeAutoCorrect: false
  VersionAdded: '2.17'

Rails/IgnoredSkipActionFilterOption:
  Description: 'Checks that `if` and `only` (or `except`) are not used together as options of `skip_*` action filter.'
  Reference: 'https://api.rubyonrails.org/classes/AbstractController/Callbacks/ClassMethods.html#method-i-_normalize_callback_options'
  Enabled: true
  VersionAdded: '0.63'
  Include:
    - app/controllers/**/*.rb
    - app/mailers/**/*.rb

Rails/IndexBy:
  Description: 'Prefer `index_by` over `each_with_object`, `to_h`, or `map`.'
  Enabled: true
  VersionAdded: '2.5'
  VersionChanged: '2.8'

Rails/IndexWith:
  Description: 'Prefer `index_with` over `each_with_object`, `to_h`, or `map`.'
  Enabled: true
  VersionAdded: '2.5'
  VersionChanged: '2.8'

Rails/Inquiry:
  Description: "Prefer Ruby's comparison operators over Active Support's `Array#inquiry` and `String#inquiry`."
  StyleGuide: 'https://rails.rubystyle.guide/#inquiry'
  Enabled: 'pending'
  VersionAdded: '2.7'

Rails/InverseOf:
  Description: 'Checks for associations where the inverse cannot be determined automatically.'
  Reference:
    - https://guides.rubyonrails.org/association_basics.html#bi-directional-associations
    - https://api.rubyonrails.org/classes/ActiveRecord/Associations/ClassMethods.html#module-ActiveRecord::Associations::ClassMethods-label-Setting+Inverses
  Enabled: true
  VersionAdded: '0.52'
  IgnoreScopes: false
  Include:
    - app/models/**/*.rb

Rails/LexicallyScopedActionFilter:
  Description: "Checks that methods specified in the filter's `only` or `except` options are explicitly defined in the class."
  StyleGuide: 'https://rails.rubystyle.guide#lexically-scoped-action-filter'
  Enabled: true
  Safe: false
  VersionAdded: '0.52'
  Include:
    - app/controllers/**/*.rb
    - app/mailers/**/*.rb

Rails/LinkToBlank:
  Description: 'Checks that `link_to` with a `target: "_blank"` have a `rel: "noopener"` option passed to them.'
  Reference:
    - https://mathiasbynens.github.io/rel-noopener/
    - https://html.spec.whatwg.org/multipage/links.html#link-type-noopener
    - https://html.spec.whatwg.org/multipage/links.html#link-type-noreferrer
  Enabled: true
  VersionAdded: '0.62'

Rails/MailerName:
  Description: 'Mailer should end with `Mailer` suffix.'
  StyleGuide: 'https://rails.rubystyle.guide/#mailer-name'
  Enabled: 'pending'
  SafeAutoCorrect: false
  VersionAdded: '2.7'
  Include:
    - app/mailers/**/*.rb

Rails/MatchRoute:
  Description: >-
                  Don't use `match` to define any routes unless there is a need to map multiple request types
                  among [:get, :post, :patch, :put, :delete] to a single action using the `:via` option.
  StyleGuide: 'https://rails.rubystyle.guide/#no-match-routes'
  Enabled: 'pending'
  VersionAdded: '2.7'
  Include:
    - config/routes.rb
    - config/routes/**/*.rb

Rails/MigrationClassName:
  Description: 'The class name of the migration should match its file name.'
  Enabled: pending
  VersionAdded: '2.14'
  VersionChanged: '2.20'
  Include:
    - db/**/*.rb

Rails/MultipleRoutePaths:
  Description: 'Checks for mapping a route with multiple paths, which is deprecated and will be removed in Rails 8.1.'
  Enabled: pending
  Severity: warning
  VersionAdded: '2.29'
  Include:
    - config/routes.rb
    - config/routes/**/*.rb

Rails/NegateInclude:
  Description: 'Prefer `collection.exclude?(obj)` over `!collection.include?(obj)`.'
  StyleGuide: 'https://rails.rubystyle.guide#exclude'
  Enabled: 'pending'
  Safe: false
  VersionAdded: '2.7'
  VersionChanged: '2.9'

Rails/NotNullColumn:
  Description: 'Do not add a NOT NULL column without a default value to existing tables.'
  Enabled: true
  VersionAdded: '0.43'
  VersionChanged: '2.20'
  Database: null
  SupportedDatabases:
    - mysql
  Include:
    - db/**/*.rb

Rails/OrderById:
  Description: >-
                  Do not use the `id` column for ordering.
                  Use a timestamp column to order chronologically.
  StyleGuide: 'https://rails.rubystyle.guide/#order-by-id'
  Enabled: false
  VersionAdded: '2.8'

Rails/Output:
  Description: 'Checks for calls to puts, print, etc.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '0.15'
  VersionChanged: '0.19'
  Include:
    - app/**/*.rb
    - config/**/*.rb
    - db/**/*.rb
    - lib/**/*.rb

Rails/OutputSafety:
  Description: 'The use of `html_safe` or `raw` may be a security risk.'
  Enabled: true
  VersionAdded: '0.41'

Rails/Pick:
  Description: 'Prefer `pick` over `pluck(...).first`.'
  StyleGuide: 'https://rails.rubystyle.guide#pick'
  Enabled: true
  Safe: false
  VersionAdded: '2.6'

Rails/Pluck:
  Description: 'Prefer `pluck` over `map { ... }`.'
  StyleGuide: 'https://rails.rubystyle.guide#pluck'
  Enabled: 'pending'
  Safe: false
  VersionAdded: '2.7'
  VersionChanged: '2.18'

Rails/PluckId:
  Description: 'Use `ids` instead of `pluck(:id)` or `pluck(primary_key)`.'
  StyleGuide: 'https://rails.rubystyle.guide/#ids'
  Enabled: false
  Safe: false
  VersionAdded: '2.7'

Rails/PluckInWhere:
  Description: 'Use `select` instead of `pluck` in `where` query methods.'
  Enabled: 'pending'
  Safe: false
  VersionAdded: '2.7'
  VersionChanged: '2.8'
  EnforcedStyle: conservative
  SupportedStyles:
    - conservative
    - aggressive

Rails/PluralizationGrammar:
  Description: 'Checks for incorrect grammar when using methods like `3.day.ago`.'
  Enabled: true
  VersionAdded: '0.35'

Rails/Presence:
  Description: 'Checks code that can be written more easily using `Object#presence` defined by Active Support.'
  Enabled: true
  VersionAdded: '0.52'

Rails/Present:
  Description: 'Enforces use of `present?`.'
  Enabled: true
  VersionAdded: '0.48'
  VersionChanged: '0.67'
  # Convert usages of `!nil? && !empty?` to `present?`
  NotNilAndNotEmpty: true
  # Convert usages of `!blank?` to `present?`
  NotBlank: true
  # Convert usages of `unless blank?` to `if present?`
  UnlessBlank: true

Rails/RakeEnvironment:
  Description: 'Include `:environment` as a dependency for all Rake tasks.'
  Enabled: true
  Safe: false
  VersionAdded: '2.4'
  VersionChanged: '2.6'
  Include:
    - '**/Rakefile'
    - '**/*.rake'
  Exclude:
    - 'lib/capistrano/tasks/**/*.rake'

Rails/ReadWriteAttribute:
  Description: >-
                 Checks for read_attribute(:attr) and
                 write_attribute(:attr, val).
  StyleGuide: 'https://rails.rubystyle.guide#read-attribute'
  Enabled: true
  VersionAdded: '0.20'
  VersionChanged: '0.29'
  Include:
    - app/models/**/*.rb

Rails/RedundantActiveRecordAllMethod:
  Description: Detect redundant `all` used as a receiver for Active Record query methods.
  StyleGuide: 'https://rails.rubystyle.guide/#redundant-all'
  Enabled: pending
  Safe: false
  AllowedReceivers:
    - ActionMailer::Preview
    - ActiveSupport::TimeZone
  VersionAdded: '2.21'

Rails/RedundantAllowNil:
  Description: >-
                 Finds redundant use of `allow_nil` when `allow_blank` is set to
                 certain values in model validations.
  Enabled: true
  VersionAdded: '0.67'
  Include:
    - app/models/**/*.rb

Rails/RedundantForeignKey:
  Description: 'Checks for associations where the `:foreign_key` option is redundant.'
  Enabled: true
  VersionAdded: '2.6'

Rails/RedundantPresenceValidationOnBelongsTo:
  Description: 'Checks for redundant presence validation on belongs_to association.'
  Enabled: pending
  SafeAutoCorrect: false
  VersionAdded: '2.13'

Rails/RedundantReceiverInWithOptions:
  Description: 'Checks for redundant receiver in `with_options`.'
  Enabled: true
  VersionAdded: '0.52'

Rails/RedundantTravelBack:
  Description: Checks for redundant `travel_back` calls.
  Enabled: pending
  VersionAdded: '2.12'
  Include:
    - spec/**/*.rb
    - test/**/*.rb

Rails/ReflectionClassName:
  Description: 'Use a string for `class_name` option value in the definition of a reflection.'
  Enabled: true
  Safe: false
  VersionAdded: '0.64'
  VersionChanged: '2.10'

Rails/RefuteMethods:
  Description: 'Use `assert_not` methods instead of `refute` methods.'
  Enabled: true
  VersionAdded: '0.56'
  EnforcedStyle: assert_not
  SupportedStyles:
    - assert_not
    - refute
  Include:
    - '**/test/**/*'

Rails/RelativeDateConstant:
  Description: 'Do not assign relative date to constants.'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '0.48'
  VersionChanged: '2.13'

Rails/RenderInline:
  Description: 'Prefer using a template over inline rendering.'
  StyleGuide: 'https://rails.rubystyle.guide/#inline-rendering'
  Enabled: 'pending'
  VersionAdded: '2.7'

Rails/RenderPlainText:
  Description: 'Prefer `render plain:` over `render text:`.'
  StyleGuide: 'https://rails.rubystyle.guide/#plain-text-rendering'
  Enabled: 'pending'
  VersionAdded: '2.7'
  # Convert only when `content_type` is explicitly set to `text/plain`.
  ContentTypeCompatibility: true

Rails/RequestReferer:
  Description: 'Use consistent syntax for request.referer.'
  Enabled: true
  VersionAdded: '0.41'
  EnforcedStyle: referer
  SupportedStyles:
    - referer
    - referrer

Rails/RequireDependency:
  Description: 'Do not use `require_dependency` when running in Zeitwerk mode. `require_dependency` is for autoloading in classic mode.'
  Reference: 'https://guides.rubyonrails.org/autoloading_and_reloading_constants.html'
  Enabled: false
  VersionAdded: '2.10'

Rails/ResponseParsedBody:
  Description: Prefer `response.parsed_body` to custom parsing logic for `response.body`.
  Enabled: pending
  Safe: false
  VersionAdded: '2.18'
  VersionChanged: '2.19'
  Include:
    - spec/controllers/**/*.rb
    - spec/requests/**/*.rb
    - test/controllers/**/*.rb
    - test/integration/**/*.rb

Rails/ReversibleMigration:
  Description: 'Checks whether the change method of the migration file is reversible.'
  StyleGuide: 'https://rails.rubystyle.guide#reversible-migration'
  Reference: 'https://api.rubyonrails.org/classes/ActiveRecord/Migration/CommandRecorder.html'
  Enabled: true
  VersionAdded: '0.47'
  VersionChanged: '2.13'
  Include:
    - db/**/*.rb

Rails/ReversibleMigrationMethodDefinition:
  Description: 'Checks whether the migration implements either a `change` method or both an `up` and a `down` method.'
  Enabled: false
  VersionAdded: '2.10'
  VersionChanged: '2.13'
  Include:
    - db/**/*.rb

Rails/RootJoinChain:
  Description: 'Use a single `#join` instead of chaining on `Rails.root` or `Rails.public_path`.'
  Enabled: pending
  VersionAdded: '2.13'

Rails/RootPathnameMethods:
  Description: 'Use `Rails.root` IO methods instead of passing it to `File`.'
  Enabled: pending
  SafeAutoCorrect: false
  VersionAdded: '2.16'

Rails/RootPublicPath:
  Description: "Favor `Rails.public_path` over `Rails.root` with `'public'`."
  Enabled: pending
  VersionAdded: '2.15'

Rails/SafeNavigation:
  Description: "Use Ruby's safe navigation operator (`&.`) instead of `try!`."
  Enabled: true
  VersionAdded: '0.43'
  # This will convert usages of `try` to use safe navigation as well as `try!`.
  # `try` and `try!` work slightly differently. `try!` and safe navigation will
  # both raise a `NoMethodError` if the receiver of the method call does not
  # implement the intended method. `try` will not raise an exception for this.
  ConvertTry: false

Rails/SafeNavigationWithBlank:
  Description: 'Avoid `foo&.blank?` in conditionals.'
  Enabled: true
  VersionAdded: '2.4'
  # While the safe navigation operator is generally a good idea, when
  # checking `foo&.blank?` in a conditional, `foo` being `nil` will actually
  # do the opposite of what the author intends.
  #
  # foo&.blank? #=> nil
  # foo.blank? #=> true
  SafeAutoCorrect: false

Rails/SaveBang:
  Description: 'Identifies possible cases where Active Record save! or related should be used.'
  StyleGuide: 'https://rails.rubystyle.guide#save-bang'
  Enabled: false
  VersionAdded: '0.42'
  VersionChanged: '0.59'
  AllowImplicitReturn: true
  AllowedReceivers: []
  SafeAutoCorrect: false

Rails/SchemaComment:
  Description: >-
    Enforces the use of the `comment` option when adding a new table or column
    to the database during a migration.
  Enabled: false
  VersionAdded: '2.13'

Rails/ScopeArgs:
  Description: 'Checks the arguments of ActiveRecord scopes.'
  Enabled: true
  VersionAdded: '0.19'
  VersionChanged: '2.12'
  Include:
    - app/models/**/*.rb

Rails/SelectMap:
  Description: 'Checks for uses of `select(:column_name)` with `map(&:column_name)`.'
  Enabled: pending
  Safe: false
  VersionAdded: '2.21'

Rails/ShortI18n:
  Description: 'Use the short form of the I18n methods: `t` instead of `translate` and `l` instead of `localize`.'
  StyleGuide: 'https://rails.rubystyle.guide/#short-i18n'
  Enabled: 'pending'
  VersionAdded: '2.7'
  EnforcedStyle: conservative
  SupportedStyles:
    - conservative
    - aggressive

Rails/SkipsModelValidations:
  Description: >-
                 Use methods that skips model validations with caution.
                 See reference for more information.
  Reference: 'https://guides.rubyonrails.org/active_record_validations.html#skipping-validations'
  Enabled: true
  Safe: false
  VersionAdded: '0.47'
  VersionChanged: '2.25'
  ForbiddenMethods:
    - decrement!
    - decrement_counter
    - increment!
    - increment_counter
    - insert
    - insert!
    - insert_all
    - insert_all!
    - toggle!
    - touch
    - touch_all
    - update_all
    - update_attribute
    - update_column
    - update_columns
    - update_counters
    - upsert
    - upsert_all
  AllowedMethods: []

Rails/SquishedSQLHeredocs:
  Description: 'Checks SQL heredocs to use `.squish`.'
  StyleGuide: 'https://rails.rubystyle.guide/#squished-heredocs'
  Enabled: 'pending'
  VersionAdded: '2.8'
  VersionChanged: '2.9'
  # Some SQL syntax (e.g. PostgreSQL comments and functions) requires newlines
  # to be preserved in order to work, thus autocorrection is not safe.
  SafeAutoCorrect: false

Rails/StripHeredoc:
  Description: 'Enforces the use of squiggly heredoc over `strip_heredoc`.'
  StyleGuide: 'https://rails.rubystyle.guide/#prefer-squiggly-heredoc'
  Enabled: pending
  VersionAdded: '2.15'

Rails/StrongParametersExpect:
  Description: 'Enforces the use of `ActionController::Parameters#expect` as a method for strong parameter handling.'
  Reference: 'https://api.rubyonrails.org/classes/ActionController/Parameters.html#method-i-expect'
  Enabled: pending
  Include:
    - app/controllers/**/*.rb
  SafeAutoCorrect: false
  VersionAdded: '2.29'

Rails/TableNameAssignment:
  Description: >-
    Do not use `self.table_name =`. Use Inflections or `table_name_prefix` instead.
  StyleGuide: 'https://rails.rubystyle.guide/#keep-ar-defaults'
  Enabled: false
  VersionAdded: '2.14'
  Include:
    - app/models/**/*.rb

Rails/ThreeStateBooleanColumn:
  Description: 'Add a default value and a `NOT NULL` constraint to boolean columns.'
  StyleGuide: 'https://rails.rubystyle.guide/#three-state-boolean'
  Enabled: pending
  VersionAdded: '2.19'
  Include:
    - db/**/*.rb

Rails/TimeZone:
  Description: 'Checks the correct usage of time zone aware methods.'
  StyleGuide: 'https://rails.rubystyle.guide#time'
  Reference: 'http://danilenko.org/2012/7/6/rails_timezones'
  Enabled: true
  SafeAutoCorrect: false
  VersionAdded: '0.30'
  VersionChanged: '2.13'
  # The value `strict` means that `Time` should be used with `zone`.
  # The value `flexible` allows usage of `in_time_zone` instead of `zone`.
  EnforcedStyle: flexible
  SupportedStyles:
    - strict
    - flexible
  Exclude:
    - '**/*.gemspec'

Rails/TimeZoneAssignment:
  Description: 'Prefer the usage of `Time.use_zone` instead of manually updating `Time.zone` value.'
  Reference: 'https://thoughtbot.com/blog/its-about-time-zones'
  Enabled: 'pending'
  VersionAdded: '2.10'
  Include:
    - spec/**/*.rb
    - test/**/*.rb

Rails/ToFormattedS:
  Description: 'Checks for consistent uses of `to_fs` or `to_formatted_s`.'
  StyleGuide: 'https://rails.rubystyle.guide/#prefer-to-fs'
  Enabled: pending
  EnforcedStyle: to_fs
  SupportedStyles:
    - to_fs
    - to_formatted_s
  VersionAdded: '2.15'

Rails/ToSWithArgument:
  Description: 'Identifies passing any argument to `#to_s`.'
  Enabled: pending
  Safe: false
  VersionAdded: '2.16'

Rails/TopLevelHashWithIndifferentAccess:
  Description: 'Identifies top-level `HashWithIndifferentAccess`.'
  Reference: 'https://guides.rubyonrails.org/upgrading_ruby_on_rails.html#top-level-hashwithindifferentaccess-is-soft-deprecated'
  Enabled: pending
  Severity: warning
  VersionAdded: '2.16'
  VersionChanged: '2.18'

Rails/TransactionExitStatement:
  Description: 'Avoid the usage of `return`, `break` and `throw` in transaction blocks.'
  Reference:
    - https://github.com/rails/rails/commit/15aa4200e083
  Enabled: pending
  VersionAdded: '2.14'
  TransactionMethods: []

Rails/UniqBeforePluck:
  Description: 'Prefer the use of uniq or distinct before pluck.'
  Enabled: true
  VersionAdded: '0.40'
  VersionChanged: '2.13'
  EnforcedStyle: conservative
  SupportedStyles:
    - conservative
    - aggressive
  SafeAutoCorrect: false

Rails/UniqueValidationWithoutIndex:
  Description: 'Uniqueness validation should have a unique index on the database column.'
  Enabled: true
  VersionAdded: '2.5'
  Include:
    - app/models/**/*.rb

Rails/UnknownEnv:
  Description: 'Use correct environment name.'
  Enabled: true
  Severity: warning
  VersionAdded: '0.51'
  VersionChanged: '2.18'
  Environments:
    - development
    - test
    - production

Rails/UnusedIgnoredColumns:
  Description: 'Remove a column that does not exist from `ignored_columns`.'
  Enabled: false
  VersionAdded: '2.11'
  VersionChanged: '2.25'
  Include:
    - app/models/**/*.rb

Rails/UnusedRenderContent:
  Description: 'Do not specify body content for a response with a non-content status code.'
  Enabled: pending
  Severity: warning
  VersionAdded: '2.21'

Rails/Validation:
  Description: 'Use validates :attribute, hash of validations.'
  Enabled: true
  VersionAdded: '0.9'
  VersionChanged: '0.41'
  Include:
    - app/models/**/*.rb

Rails/WhereEquals:
  Description: 'Pass conditions to `where` and `where.not` as a hash instead of manually constructing SQL.'
  StyleGuide: 'https://rails.rubystyle.guide/#hash-conditions'
  Enabled: 'pending'
  SafeAutoCorrect: false
  VersionAdded: '2.9'
  VersionChanged: '2.26'

Rails/WhereExists:
  Description: 'Prefer `exists?(...)` over `where(...).exists?`.'
  Enabled: 'pending'
  SafeAutoCorrect: false
  EnforcedStyle: exists
  SupportedStyles:
    - exists
    - where
  VersionAdded: '2.7'
  VersionChanged: '2.10'

Rails/WhereMissing:
  Description: 'Use `where.missing(...)` to find missing relationship records.'
  StyleGuide: 'https://rails.rubystyle.guide/#finding-missing-relationship-records'
  Enabled: pending
  VersionAdded: '2.16'

Rails/WhereNot:
  Description: 'Use `where.not(...)` instead of manually constructing negated SQL in `where`.'
  StyleGuide: 'https://rails.rubystyle.guide/#hash-conditions'
  Enabled: 'pending'
  VersionAdded: '2.8'

Rails/WhereNotWithMultipleConditions:
  Description: 'Do not use `where.not(...)` with multiple conditions.'
  StyleGuide: 'https://rails.rubystyle.guide/#where-not-with-multiple-attributes'
  Enabled: 'pending'
  Severity: warning
  VersionAdded: '2.17'
  VersionChanged: '2.18'

Rails/WhereRange:
  Description: 'Use ranges in `where` instead of manually constructing SQL.'
  StyleGuide: 'https://rails.rubystyle.guide/#where-ranges'
  Enabled: pending
  SafeAutoCorrect: false
  VersionAdded: '2.25'

# Accept `redirect_to(...) and return` and similar cases.
Style/AndOr:
  EnforcedStyle: conditionals

Style/CollectionCompact:
  AllowedReceivers:
    - params

Style/FormatStringToken:
  AllowedMethods:
    - redirect

Style/InvertibleUnlessCondition:
  InverseMethods:
    # Active Support defines some common inverse methods. They are listed below:
    :present?: :blank?
    :blank?: :present?
    :include?: :exclude?
    :exclude?: :include?

Style/SymbolProc:
  AllowedMethods:
    - define_method
    - mail
    - respond_to
