
# Summary

Describe an overview of the problem. Provide a general description of the problem it's solving or why this issue should be addressed.

## Things out of Scope
> **OPTIONAL** List here work that is related and shouldn't be done as part of this issue. This can be work we've decided to never do, or it can be work we've decided to put in a separate issue.

## Things to Consider
> **OPTIONAL** If you happen to know that this particular issue will touch other parts of the application in non-obvious ways (maybe there are side effects, or there are other parts of the application that depend on the thing that needs to be changed), you can list them out here. This is an opportunity to share *domain knowledge* that you might have about the problemset.

# Criteria for Completion
> This should be a bulleted list of items that, if satisfied, would result in an acceptably complete Pull Request. Be as specific as you need to be. For example:

 - [ ] When a user clicks on {this} it should now do {that}
 - [ ] This feature should only be available to admins
 - [ ] Add a test proving that it works

