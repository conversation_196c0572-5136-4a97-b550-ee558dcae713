# syntax = docker/dockerfile:1

# Make sure RUBY_VERSION matches the Ruby version in .ruby-version and Gemfile
ARG RUBY_VERSION=3.4.4
FROM ruby:$RUBY_VERSION-slim as base

# Rails app lives here
WORKDIR /rails

# Install packages needed to build gems
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y \
    build-essential libpq-dev curl \
    nodejs npm libyaml-dev git \
    chromium chromium-driver # Add these packages \
    build-essential libpq-dev curl \
    nodejs npm libyaml-dev git && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
RUN npm install -g yarn

COPY Gemfile Gemfile.lock ./

# Install gems - ensure we install all groups
RUN gem update --system && \
    gem install bundler && \
    bundle config set --local without '' && \
    bundle install --jobs 20 --retry 5

COPY . .
RUN SECRET_KEY_BASE=placeholder bundle exec rails assets:precompile

# Start the server by default, this can be overwritten at runtime
EXPOSE 3000
CMD ["./bin/rails", "server", "-b", "0.0.0.0"]
